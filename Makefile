APP_NAMES := migrate backend frontend
DOCKER_REPO := warda
CLUSTER_NAME := warda-dev
REGISTRY := warda-dev-registry:5001

.PHONY: all build k3d-setup k3d-build k3d-deploy k3d-delete helm-deploy helm-delete build-multiarch docker-clean

# ---- Build All Rust Apps ----
build:
	cargo build --workspace --release

# ---- k3d Cluster Setup ----
k3d-setup:
	@echo "🔧 Setting up k3d cluster..."
	@if ! k3d cluster list | grep -q "$(CLUSTER_NAME)"; then \
		echo "🚀 Creating k3d cluster: $(CLUSTER_NAME)"; \
		k3d cluster create --config k3d-config.yaml; \
	else \
		echo "✅ k3d cluster $(CLUSTER_NAME) already exists"; \
		k3d cluster start $(CLUSTER_NAME) || true; \
	fi
	@echo "📦 Updating Helm dependencies..."
	@cd helm/warda && helm dependency update
	@echo "✅ k3d setup complete"

# ---- Multi-arch Docker Build ----
build-multiarch:
	@echo "🚀 Building multi-arch Docker images..."
	@docker buildx create --name multiarch-builder --use || true
	@mkdir -p /tmp/.buildx-cache-migrate /tmp/.buildx-cache-backend /tmp/.buildx-cache-frontend
	@echo "📦 Building migrate (multi-arch)..."
	@docker buildx build \
		--platform linux/amd64,linux/arm64 \
		--file apps/migrate/Dockerfile \
		--tag $(REGISTRY)/warda/migrate:latest \
		--cache-from type=local,src=/tmp/.buildx-cache-migrate \
		--cache-to type=local,dest=/tmp/.buildx-cache-migrate,mode=max \
		--push .
	@echo "📦 Building backend (multi-arch)..."
	@docker buildx build \
		--platform linux/amd64,linux/arm64 \
		--file apps/backend/Dockerfile \
		--tag $(REGISTRY)/warda/backend:latest \
		--cache-from type=local,src=/tmp/.buildx-cache-backend \
		--cache-to type=local,dest=/tmp/.buildx-cache-backend,mode=max \
		--push .
	@echo "📦 Building frontend (multi-arch)..."
	@docker buildx build \
		--platform linux/amd64,linux/arm64 \
		--file apps/frontend/Dockerfile \
		--tag $(REGISTRY)/warda/frontend:latest \
		--cache-from type=local,src=/tmp/.buildx-cache-frontend \
		--cache-to type=local,dest=/tmp/.buildx-cache-frontend,mode=max \
		--push .
	@echo "✅ Multi-arch images built and pushed to local registry"

# ---- Local k3d Build (single arch, faster for development) ----
k3d-build:
	@echo "🚀 Building Docker images for k3d..."
	@echo "📦 Building migrate..."
	@docker build \
		--file apps/migrate/Dockerfile \
		--tag $(REGISTRY)/warda/migrate:latest \
		.
	@echo "📦 Building backend..."
	@docker build \
		--file apps/backend/Dockerfile \
		--tag $(REGISTRY)/warda/backend:latest \
		.
	@echo "📦 Building frontend..."
	@docker build \
		--file apps/frontend/Dockerfile \
		--tag $(REGISTRY)/warda/frontend:latest \
		.
	@echo "📤 Pushing images to k3d registry..."
	@docker tag $(REGISTRY)/warda/migrate:latest localhost:5001/warda/migrate:latest
	@docker tag $(REGISTRY)/warda/backend:latest localhost:5001/warda/backend:latest
	@docker tag $(REGISTRY)/warda/frontend:latest localhost:5001/warda/frontend:latest
	@docker push localhost:5001/warda/migrate:latest
	@docker push localhost:5001/warda/backend:latest
	@docker push localhost:5001/warda/frontend:latest
	@echo "✅ Images built and pushed to k3d registry"

# ---- Parallel k3d Build (faster for development) ----
k3d-build-parallel:
	@echo "🚀 Building Docker images for k3d in parallel..."
	@echo "📦 Starting parallel builds..."
	@(docker build \
		--file apps/migrate/Dockerfile \
		--tag $(REGISTRY)/warda/migrate:latest \
		. && echo "✅ Migrate built") & \
	(docker build \
		--file apps/backend/Dockerfile \
		--tag $(REGISTRY)/warda/backend:latest \
		. && echo "✅ Backend built") & \
	(docker build \
		--file apps/frontend/Dockerfile \
		--tag $(REGISTRY)/warda/frontend:latest \
		. && echo "✅ Frontend built") & \
	wait
	@echo "📤 Pushing images to k3d registry..."
	@docker tag $(REGISTRY)/warda/migrate:latest localhost:5001/warda/migrate:latest
	@docker tag $(REGISTRY)/warda/backend:latest localhost:5001/warda/backend:latest
	@docker tag $(REGISTRY)/warda/frontend:latest localhost:5001/warda/frontend:latest
	@docker push localhost:5001/warda/migrate:latest & \
	docker push localhost:5001/warda/backend:latest & \
	docker push localhost:5001/warda/frontend:latest & \
	wait
	@echo "✅ All images built and pushed to k3d registry"

# ---- Build and Deploy ----
k3d-deploy: k3d-setup k3d-build
	$(MAKE) helm-deploy

# ---- Fast Build and Deploy (parallel) ----
k3d-deploy-fast: k3d-setup k3d-build-parallel
	$(MAKE) helm-deploy

# ---- Helm Deploy ----
helm-deploy:
	@echo "🚀 Deploying with Helm..."
	helm upgrade --install warda helm/warda -f helm/warda/values.yaml \
		--timeout=300s \
		--wait \
		--wait-for-jobs
	@echo "✅ Deployment completed successfully"

# ---- Helm Deploy for Remote Cluster ----
helm-deploy-remote:
	@echo "🚀 Deploying to remote cluster with Helm..."
	helm upgrade --install warda helm/warda \
		-f helm/warda/values.yaml \
		-f helm/warda/values-remote.yaml \
		--timeout=300s \
		--wait \
		--wait-for-jobs
	@echo "✅ Remote deployment completed successfully"

# ---- Helm Deploy with Custom Host ----
helm-deploy-host:
	@if [ -z "$(HOST)" ]; then \
		echo "❌ Please specify HOST. Example: make helm-deploy-host HOST=*************"; \
		exit 1; \
	fi
	@echo "🚀 Deploying with custom host: $(HOST)"
	helm upgrade --install warda helm/warda -f helm/warda/values.yaml \
		--set global.clusterHost=$(HOST) \
		--timeout=300s \
		--wait \
		--wait-for-jobs
	@echo "✅ Deployment completed successfully for host: $(HOST)"

# ---- Helm Teardown ----
helm-delete:
	helm uninstall warda

# ---- k3d Cluster Management ----
k3d-delete:
	@echo "🗑️ Deleting k3d cluster..."
	k3d cluster delete $(CLUSTER_NAME)

k3d-restart:
	@echo "🔄 Restarting k3d cluster..."
	k3d cluster stop $(CLUSTER_NAME) || true
	k3d cluster start $(CLUSTER_NAME)

# ---- Service Access ----
services:
	@echo "🌐 Service URLs:"
	@echo "Frontend:  http://localhost:30090"
	@echo "Backend:   http://localhost:30091"
	@echo "Keycloak:  http://localhost:30092"

# ---- Development Workflow ----
dev: k3d-deploy-fast services
	@echo "🎉 Development environment ready!"

# ---- Production Multi-arch Build ----
prod-build: build-multiarch
	@echo "🚀 Production multi-arch images built"

# ---- Quick rebuild and redeploy ----
redeploy: k3d-build-parallel helm-deploy
	@echo "🔄 Quick redeploy completed!"

# ---- Docker Utilities ----
docker-clean:
	@echo "🧹 Cleaning Docker cache..."
	@docker system prune -f
	@docker builder prune -f
	@docker buildx prune -f

# ---- Legacy targets for compatibility ----
all: dev